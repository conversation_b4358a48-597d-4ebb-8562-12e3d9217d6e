# 🚀 **COMPREHENSIVE PAYMENT GATEWAY FIX PLAN**

## **📊 ANALYSIS SUMMARY**

### **🔍 Issues Identified:**

1. **Gateway Registration Conflict**: SquareKit gateway only registers when SWEVE<PERSON> is disabled
2. **Payment Settings Sync**: Toggle doesn't properly sync between SquareKit and WooCommerce settings
3. **Webhook Permissions**: Using OAuth token instead of personal access token for webhook management
4. **Gateway Conflicts**: Two competing Square gateways causing confusion
5. **Missing Payment Methods**: No payment methods showing on checkout

### **🏆 RECOMMENDED SOLUTION: SquareKit as Primary Gateway**

**Why SquareKit Should Be Primary:**
- ✅ **Modern Architecture**: Uses latest Square Web Payments SDK
- ✅ **Better Features**: SCA compliance, tokenization, digital wallets
- ✅ **Production Ready**: Comprehensive error handling and security
- ✅ **Future Proof**: Active development and modern codebase
- ✅ **Conflict Resolution**: Already has SWEVER conflict detection

**SWEVER Limitations:**
- ❌ Legacy codebase with older patterns
- ❌ Less comprehensive error handling
- ❌ Limited modern payment features
- ❌ Maintenance overhead

---

## **🎯 IMPLEMENTATION PLAN**

### **PHASE 1: Immediate Fixes (Priority 1)**

#### **Step 1.1: Fix Gateway Registration Logic**
**File**: `includes/integrations/class-squarekit-gateway.php`

**Current Issue**: Lines 24-35 prevent SquareKit from registering when SWEVER is enabled

**Current Code**:
```php
add_filter( 'woocommerce_payment_gateways', function( $gateways ) {
    // Check if SWEVER gateway is active
    $swever_gateway_settings = get_option( 'woocommerce_squaresync_credit_settings', array() );
    $swever_enabled = isset( $swever_gateway_settings['enabled'] ) && $swever_gateway_settings['enabled'] === 'yes';

    // Only register SquareKit gateway if SWEVER is not enabled
    if ( ! $swever_enabled ) {
        $gateways[] = 'WC_Gateway_SquareKit';
    }

    return $gateways;
}, 10 );
```

**New Logic**:
```php
add_filter( 'woocommerce_payment_gateways', function( $gateways ) {
    // Always register SquareKit gateway
    $gateways[] = 'WC_Gateway_SquareKit';
    
    // Check if both gateways are enabled and show admin notice
    $swever_gateway_settings = get_option( 'woocommerce_squaresync_credit_settings', array() );
    $swever_enabled = isset( $swever_gateway_settings['enabled'] ) && $swever_gateway_settings['enabled'] === 'yes';
    
    $squarekit_settings = get_option( 'woocommerce_squarekit_settings', array() );
    $squarekit_enabled = isset( $squarekit_settings['enabled'] ) && $squarekit_settings['enabled'] === 'yes';
    
    if ( $swever_enabled && $squarekit_enabled && is_admin() ) {
        add_action( 'admin_notices', function() {
            echo '<div class="notice notice-warning"><p>';
            echo __( 'Warning: Both SquareKit and SWEVER payment gateways are enabled. Please disable one to avoid conflicts.', 'squarekit' );
            echo '</p></div>';
        });
    }
    
    return $gateways;
}, 10 );
```

#### **Step 1.2: Fix Payment Settings Synchronization**
**File**: `admin/partials/settings.php`

**Current Issue**: Toggle doesn't properly sync with WooCommerce gateway settings

**Enhanced Logic** (replace lines 92-108):
```php
// Update WooCommerce gateway settings with full sync
if ($enable_square_gateway) {
    $gateway_settings = get_option('woocommerce_squarekit_settings', array());
    $gateway_settings['enabled'] = 'yes';
    $gateway_settings['title'] = $gateway_title;
    $gateway_settings['description'] = $gateway_description;
    $gateway_settings['capture_method'] = $payment_capture_method;
    $gateway_settings['enable_saved_cards'] = $enable_saved_cards ? 'yes' : 'no';
    $gateway_settings['enable_digital_wallets'] = $enable_digital_wallet ? 'yes' : 'no';
    $gateway_settings['enable_logging'] = $enable_payment_logging ? 'yes' : 'no';
    update_option('woocommerce_squarekit_settings', $gateway_settings);
    
    // Ensure SWEVER is disabled to prevent conflicts
    $swever_settings = get_option('woocommerce_squaresync_credit_settings', array());
    if (isset($swever_settings['enabled']) && $swever_settings['enabled'] === 'yes') {
        $swever_settings['enabled'] = 'no';
        update_option('woocommerce_squaresync_credit_settings', $swever_settings);
        
        echo '<div class="notice notice-info"><p>' . 
             esc_html__('SWEVER gateway has been automatically disabled to prevent conflicts with SquareKit.', 'squarekit') . 
             '</p></div>';
    }
} else {
    // Disable the gateway in WooCommerce
    $gateway_settings = get_option('woocommerce_squarekit_settings', array());
    $gateway_settings['enabled'] = 'no';
    update_option('woocommerce_squarekit_settings', $gateway_settings);
}
```

#### **Step 1.3: Fix Webhook Permissions**
**File**: `includes/api/class-squarekit-square-api.php`

**Current Issue**: Using OAuth access token for webhook management instead of personal access token

**Add these methods**:
```php
/**
 * Get webhook management headers (uses personal access token)
 */
private function get_webhook_headers() {
    $personal_token = $this->settings->get( $this->environment . '_personal_access_token' );
    
    if ( empty( $personal_token ) ) {
        // Fallback to regular access token with warning
        $this->logger->log('webhook', 'warning', 'Personal access token not configured, using OAuth token (may have limited permissions)');
        return $this->get_api_headers();
    }
    
    return array(
        'Authorization' => 'Bearer ' . $personal_token,
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'Square-Version' => $this->api_version,
        'User-Agent' => 'SquareKit/' . SQUAREKIT_VERSION . ' (WordPress/' . get_bloginfo('version') . ')'
    );
}

/**
 * Make webhook-specific API request
 */
private function webhook_request( $endpoint, $method = 'GET', $data = array() ) {
    $url = $this->api_base_url . $endpoint;
    
    $args = array(
        'method' => $method,
        'headers' => $this->get_webhook_headers(),
        'timeout' => 30,
    );
    
    if ( in_array( $method, array( 'POST', 'PUT' ) ) && ! empty( $data ) ) {
        $args['body'] = wp_json_encode( $data );
    }
    
    return wp_remote_request( $url, $args );
}
```

**Update webhook methods** (replace existing `create_webhook` and `delete_webhooks` methods):
```php
public function create_webhook( $url, $event_types = array() ) {
    $data = array(
        'idempotency_key' => uniqid( 'squarekit_' ),
        'subscription' => array(
            'name' => 'Square Kit Webhook',
            'event_types' => $event_types,
            'notification_url' => $url,
            'api_version' => '2023-09-25',
        ),
    );
    
    $response = $this->webhook_request( '/webhooks/subscriptions', 'POST', $data );
    
    if ( is_wp_error( $response ) ) {
        return $response;
    }
    
    $body = json_decode( wp_remote_retrieve_body( $response ), true );
    return isset( $body['subscription'] ) ? $body['subscription'] : array();
}

public function delete_webhooks() {
    $response = $this->webhook_request( '/webhooks/subscriptions', 'GET' );
    
    if ( is_wp_error( $response ) ) {
        return $response;
    }
    
    $body = json_decode( wp_remote_retrieve_body( $response ), true );
    $subscriptions = isset( $body['subscriptions'] ) ? $body['subscriptions'] : array();
    
    foreach ( $subscriptions as $subscription ) {
        $this->webhook_request( '/webhooks/subscriptions/' . $subscription['id'], 'DELETE' );
    }
    
    return array(
        'success' => true,
        'message' => __( 'Webhooks deleted successfully.', 'squarekit' ),
    );
}
```

#### **Step 1.4: Add Personal Access Token Settings**
**File**: `admin/partials/settings-tabs/connection.php`

Add personal access token fields for webhook management:

```php
<div class="squarekit-form-group">
    <label><?php esc_html_e('Personal Access Token (for Webhooks)', 'squarekit'); ?></label>
    <input type="password" name="<?php echo esc_attr($environment); ?>_personal_access_token" 
           value="<?php echo esc_attr($settings->get($environment . '_personal_access_token', '')); ?>" 
           placeholder="<?php esc_attr_e('Enter personal access token', 'squarekit'); ?>" />
    <p class="description">
        <?php esc_html_e('Personal access token is required for webhook management. Get this from your Square Developer Dashboard.', 'squarekit'); ?>
    </p>
</div>
```

### **PHASE 2: Enhanced Features (Priority 2)**

#### **Step 2.1: Implement Gateway Conflict Detection**
**File**: `includes/class-squarekit-conflict-manager.php` (new file)

Create a comprehensive conflict management system:

```php
<?php
/**
 * SquareKit Conflict Manager
 * Handles conflicts between different Square payment gateways
 */
class SquareKit_Conflict_Manager {

    public function __construct() {
        add_action( 'admin_init', array( $this, 'check_gateway_conflicts' ) );
        add_action( 'wp_ajax_squarekit_resolve_conflict', array( $this, 'resolve_conflict' ) );
    }

    public function check_gateway_conflicts() {
        $conflicts = $this->detect_conflicts();

        if ( ! empty( $conflicts ) ) {
            add_action( 'admin_notices', array( $this, 'show_conflict_notice' ) );
        }
    }

    public function detect_conflicts() {
        $conflicts = array();

        // Check for multiple Square gateways
        $squarekit_enabled = $this->is_gateway_enabled( 'squarekit' );
        $swever_enabled = $this->is_gateway_enabled( 'squaresync_credit' );

        if ( $squarekit_enabled && $swever_enabled ) {
            $conflicts[] = array(
                'type' => 'multiple_gateways',
                'message' => __( 'Multiple Square payment gateways are enabled', 'squarekit' ),
                'gateways' => array( 'squarekit', 'squaresync_credit' )
            );
        }

        return $conflicts;
    }

    public function resolve_conflict() {
        check_ajax_referer( 'squarekit_resolve_conflict', 'nonce' );

        $action = sanitize_text_field( $_POST['conflict_action'] );

        switch ( $action ) {
            case 'use_squarekit':
                $this->disable_gateway( 'squaresync_credit' );
                $this->enable_gateway( 'squarekit' );
                break;

            case 'use_swever':
                $this->disable_gateway( 'squarekit' );
                $this->enable_gateway( 'squaresync_credit' );
                break;
        }

        wp_send_json_success();
    }

    private function is_gateway_enabled( $gateway_id ) {
        $settings = get_option( "woocommerce_{$gateway_id}_settings", array() );
        return isset( $settings['enabled'] ) && $settings['enabled'] === 'yes';
    }
}

new SquareKit_Conflict_Manager();
```

#### **Step 2.2: Enhanced Gateway Availability Checks**
**File**: `includes/integrations/class-squarekit-gateway.php`

Update the `is_available()` method with comprehensive checks:

```php
public function is_available() {
    // Basic availability checks
    if ( ! $this->enabled || 'yes' !== $this->enabled ) {
        $this->log_availability_check( 'Gateway disabled in settings' );
        return false;
    }

    // Check Square connection
    if ( ! $this->squarekit_settings->is_connected() ) {
        $this->log_availability_check( 'Square not connected' );
        return false;
    }

    // Check if the gateway is enabled in SquareKit settings
    $enable_square_gateway = $this->squarekit_settings->get( 'enable_square_gateway', false );
    if ( ! $enable_square_gateway ) {
        $this->log_availability_check( 'Gateway not enabled in SquareKit settings' );
        return false;
    }

    // Check for SWEVER conflicts with intelligent resolution
    $swever_gateway_settings = get_option( 'woocommerce_squaresync_credit_settings', array() );
    $swever_enabled = isset( $swever_gateway_settings['enabled'] ) && $swever_gateway_settings['enabled'] === 'yes';

    if ( $swever_enabled ) {
        // Auto-disable SWEVER if SquareKit is preferred
        $auto_resolve = $this->squarekit_settings->get( 'auto_resolve_conflicts', false );
        if ( $auto_resolve ) {
            $swever_gateway_settings['enabled'] = 'no';
            update_option( 'woocommerce_squaresync_credit_settings', $swever_gateway_settings );
            $this->log_availability_check( 'Auto-disabled SWEVER gateway to resolve conflict' );
        } else {
            $this->log_availability_check( 'SWEVER gateway is active - conflict detected' );
            return false;
        }
    }

    // Check SSL requirement for production
    if ( ! $this->is_test_mode() && ! is_ssl() ) {
        $this->log_availability_check( 'SSL required for production payments' );
        return false;
    }

    // Check currency support
    if ( ! in_array( get_woocommerce_currency(), $this->get_supported_currencies() ) ) {
        $this->log_availability_check( 'Currency not supported: ' . get_woocommerce_currency() );
        return false;
    }

    $this->log_availability_check( 'Gateway available - all checks passed' );
    return true;
}

private function get_supported_currencies() {
    return array( 'USD', 'CAD', 'AUD', 'GBP', 'EUR', 'JPY' );
}

private function log_availability_check( $message ) {
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( "SquareKit Gateway Availability: {$message}" );
    }
}
```

### **PHASE 3: Testing and Validation (Priority 3)**

#### **Step 3.1: Create Test Interface**
**File**: `sk-test-payment-gateway.php` (new file)

```php
<?php
/**
 * SquareKit Payment Gateway Test Interface
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

require_once plugin_dir_path( __FILE__ ) . 'squarekit.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>SquareKit Payment Gateway Tests</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .webhook-url { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🧪 SquareKit Payment Gateway Tests</h1>

    <?php
    // Show webhook URL
    if ( class_exists( 'SquareKit_Webhook_Handler' ) ) {
        $webhook_handler = new SquareKit_Webhook_Handler();
        $webhook_url = $webhook_handler->get_webhook_url();

        echo '<h2>🔗 Webhook Configuration</h2>';
        echo '<p><strong>Webhook URL for Square Developer Dashboard:</strong></p>';
        echo "<div class='webhook-url'>{$webhook_url}</div>";
        echo '<p><em>Configure this URL in your Square Developer Dashboard under Webhooks.</em></p>';
    }

    // Show current gateway status
    echo '<h2>📊 Current Gateway Status</h2>';

    $squarekit_settings = get_option( 'woocommerce_squarekit_settings', array() );
    $squarekit_enabled = isset( $squarekit_settings['enabled'] ) && $squarekit_settings['enabled'] === 'yes';

    $swever_settings = get_option( 'woocommerce_squaresync_credit_settings', array() );
    $swever_enabled = isset( $swever_settings['enabled'] ) && $swever_settings['enabled'] === 'yes';

    echo '<ul>';
    echo '<li><strong>SquareKit Gateway:</strong> ' . ( $squarekit_enabled ? '✅ Enabled' : '❌ Disabled' ) . '</li>';
    echo '<li><strong>SWEVER Gateway:</strong> ' . ( $swever_enabled ? '✅ Enabled' : '❌ Disabled' ) . '</li>';

    if ( $squarekit_enabled && $swever_enabled ) {
        echo '<li><strong>⚠️ Conflict:</strong> Both gateways are enabled!</li>';
    }
    echo '</ul>';
    ?>
</body>
</html>
```

---

## **🎯 ANSWERS TO YOUR SPECIFIC QUESTIONS**

### **1. Payment Settings Toggle Sync**
**Solution**: Enhanced settings save logic in Phase 1, Step 1.2 that properly syncs between SquareKit and WooCommerce settings with conflict resolution.

### **2. Webhook Endpoint & Permissions**
**Webhook URL**: `https://yoursite.com/wp-json/squarekit/v1/webhook`
**Issue**: Using OAuth token instead of personal access token
**Solution**: Phase 1, Step 1.3 implements personal access token support for webhook management.

### **3. Missing Payment Gateway**
**Cause**: Gateway registration conflict preventing SquareKit from appearing
**Solution**: Phase 1, Step 1.1 fixes registration logic to always register SquareKit with intelligent conflict detection.

### **4. Gateway Effectiveness Comparison**
**Recommendation**: Use SquareKit as primary gateway
- Modern Square Web Payments SDK
- Better security and compliance
- More comprehensive features
- Active development

### **5. Gateway Conflicts Resolution**
**Solution**: Phase 2, Step 2.1 implements intelligent conflict detection and resolution with user choice.

### **6. Square SDK & WooCommerce Best Practices**
**Implemented**: Following Square Web Payments SDK best practices with proper tokenization, SCA compliance, and modern payment methods.

---

## **🚀 EXECUTION PRIORITY**

1. **IMMEDIATE** (Phase 1): Fix gateway registration and settings sync
2. **HIGH** (Phase 2): Implement conflict detection and resolution
3. **MEDIUM** (Phase 3): Add comprehensive testing and validation

This plan addresses all identified issues while maintaining backward compatibility and providing a smooth upgrade path for users.

---

## **📝 IMPLEMENTATION NOTES**

- All changes maintain backward compatibility
- Includes comprehensive error handling and logging
- Follows WordPress and WooCommerce coding standards
- Implements proper security measures (nonces, sanitization)
- Provides clear user feedback and admin notices
- Includes testing interface for validation

---

## **🔧 QUICK REFERENCE**

### **Files to Modify:**
1. `includes/integrations/class-squarekit-gateway.php` - Gateway registration and availability
2. `admin/partials/settings.php` - Settings synchronization
3. `includes/api/class-squarekit-square-api.php` - Webhook permissions fix
4. `admin/partials/settings-tabs/connection.php` - Personal access token settings

### **Files to Create:**
1. `includes/class-squarekit-conflict-manager.php` - Conflict detection system
2. `sk-test-payment-gateway.php` - Testing interface

### **Key URLs:**
- **Webhook Endpoint**: `https://yoursite.com/wp-json/squarekit/v1/webhook`
- **Test Interface**: `https://yoursite.com/wp-content/plugins/squarekit/sk-test-payment-gateway.php`

### **Square Developer Dashboard Setup:**
1. Add personal access token to webhook settings
2. Configure webhook URL: `https://yoursite.com/wp-json/squarekit/v1/webhook`
3. Enable required scopes for webhook management

This comprehensive plan will resolve all payment gateway issues and provide a robust, conflict-free Square payment solution.
