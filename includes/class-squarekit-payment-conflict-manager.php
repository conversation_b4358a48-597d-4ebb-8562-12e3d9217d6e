<?php
/**
 * SquareKit Payment Gateway Conflict Manager
 * Handles conflicts between different Square payment gateways
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Payment Gateway Conflict Manager Class
 *
 * Provides comprehensive conflict detection and resolution for competing Square gateways
 */
class SquareKit_Payment_Conflict_Manager {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action( 'admin_init', array( $this, 'check_gateway_conflicts' ) );
        add_action( 'wp_ajax_squarekit_resolve_payment_conflict', array( $this, 'resolve_conflict' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_conflict_scripts' ) );
    }

    /**
     * Check for gateway conflicts on admin pages
     *
     * @since 1.0.0
     */
    public function check_gateway_conflicts() {
        if ( ! is_admin() ) {
            return;
        }

        $conflicts = $this->detect_conflicts();

        if ( ! empty( $conflicts ) ) {
            add_action( 'admin_notices', array( $this, 'show_conflict_notice' ) );
        }
    }

    /**
     * Detect conflicts between Square gateways
     *
     * @since 1.0.0
     * @return array Array of detected conflicts
     */
    public function detect_conflicts() {
        $conflicts = array();

        // Check for multiple Square gateways
        $squarekit_enabled = $this->is_gateway_enabled( 'squarekit' );
        $swever_enabled = $this->is_gateway_enabled( 'squaresync_credit' );

        if ( $squarekit_enabled && $swever_enabled ) {
            $conflicts[] = array(
                'type' => 'multiple_gateways',
                'message' => __( 'Multiple Square payment gateways are enabled', 'squarekit' ),
                'gateways' => array( 'squarekit', 'squaresync_credit' ),
                'severity' => 'high',
                'auto_resolvable' => true
            );
        }

        // Check for webhook conflicts
        $webhook_conflicts = $this->detect_webhook_conflicts();
        if ( ! empty( $webhook_conflicts ) ) {
            $conflicts = array_merge( $conflicts, $webhook_conflicts );
        }

        return $conflicts;
    }

    /**
     * Detect webhook conflicts
     *
     * @since 1.0.0
     * @return array Array of webhook conflicts
     */
    private function detect_webhook_conflicts() {
        $conflicts = array();

        // Check if both plugins are trying to register webhooks
        $squarekit_webhooks = get_option( 'square-kit_settings', array() );
        $swever_webhooks = get_option( 'square-woo-sync_settings', array() );

        $squarekit_webhook_enabled = isset( $squarekit_webhooks['webhook_status'] ) && $squarekit_webhooks['webhook_status'];
        $swever_webhook_enabled = isset( $swever_webhooks['webhooks']['enabled'] ) && $swever_webhooks['webhooks']['enabled'];

        if ( $squarekit_webhook_enabled && $swever_webhook_enabled ) {
            $conflicts[] = array(
                'type' => 'webhook_conflict',
                'message' => __( 'Multiple plugins are trying to register Square webhooks', 'squarekit' ),
                'severity' => 'medium',
                'auto_resolvable' => false
            );
        }

        return $conflicts;
    }

    /**
     * Show conflict notice in admin
     *
     * @since 1.0.0
     */
    public function show_conflict_notice() {
        $conflicts = $this->detect_conflicts();

        if ( empty( $conflicts ) ) {
            return;
        }

        foreach ( $conflicts as $conflict ) {
            $severity_class = 'notice-' . ( $conflict['severity'] === 'high' ? 'error' : 'warning' );
            ?>
            <div class="notice <?php echo esc_attr( $severity_class ); ?> squarekit-payment-conflict-notice">
                <p>
                    <strong><?php esc_html_e( 'SquareKit Payment Gateway Conflict Detected:', 'squarekit' ); ?></strong>
                    <?php echo esc_html( $conflict['message'] ); ?>
                </p>
                
                <?php if ( $conflict['type'] === 'multiple_gateways' && $conflict['auto_resolvable'] ): ?>
                    <p>
                        <button type="button" class="button button-primary" onclick="squareKitResolvePaymentConflict('use_squarekit')">
                            <?php esc_html_e( 'Use SquareKit (Recommended)', 'squarekit' ); ?>
                        </button>
                        <button type="button" class="button" onclick="squareKitResolvePaymentConflict('use_swever')">
                            <?php esc_html_e( 'Use SWEVER', 'squarekit' ); ?>
                        </button>
                        <button type="button" class="button" onclick="squareKitDismissPaymentConflict()">
                            <?php esc_html_e( 'Dismiss', 'squarekit' ); ?>
                        </button>
                    </p>
                <?php endif; ?>
            </div>
            <?php
        }
    }

    /**
     * AJAX handler for resolving conflicts
     *
     * @since 1.0.0
     */
    public function resolve_conflict() {
        check_ajax_referer( 'squarekit_resolve_payment_conflict', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ) );
        }

        $action = sanitize_text_field( $_POST['conflict_action'] );

        switch ( $action ) {
            case 'use_squarekit':
                $this->disable_gateway( 'squaresync_credit' );
                $this->enable_gateway( 'squarekit' );
                $message = __( 'SquareKit is now the active Square payment gateway.', 'squarekit' );
                break;

            case 'use_swever':
                $this->disable_gateway( 'squarekit' );
                $this->enable_gateway( 'squaresync_credit' );
                $message = __( 'SWEVER is now the active Square payment gateway.', 'squarekit' );
                break;

            default:
                wp_send_json_error( array( 'message' => __( 'Invalid action.', 'squarekit' ) ) );
        }

        wp_send_json_success( array( 'message' => $message ) );
    }

    /**
     * Check if a gateway is enabled
     *
     * @since 1.0.0
     * @param string $gateway_id Gateway ID
     * @return bool True if enabled, false otherwise
     */
    private function is_gateway_enabled( $gateway_id ) {
        $settings = get_option( "woocommerce_{$gateway_id}_settings", array() );
        return isset( $settings['enabled'] ) && $settings['enabled'] === 'yes';
    }

    /**
     * Enable a gateway
     *
     * @since 1.0.0
     * @param string $gateway_id Gateway ID
     * @return bool True on success, false on failure
     */
    private function enable_gateway( $gateway_id ) {
        $settings = get_option( "woocommerce_{$gateway_id}_settings", array() );
        $settings['enabled'] = 'yes';
        return update_option( "woocommerce_{$gateway_id}_settings", $settings );
    }

    /**
     * Disable a gateway
     *
     * @since 1.0.0
     * @param string $gateway_id Gateway ID
     * @return bool True on success, false on failure
     */
    private function disable_gateway( $gateway_id ) {
        $settings = get_option( "woocommerce_{$gateway_id}_settings", array() );
        $settings['enabled'] = 'no';
        return update_option( "woocommerce_{$gateway_id}_settings", $settings );
    }

    /**
     * Enqueue conflict resolution scripts
     *
     * @since 1.0.0
     */
    public function enqueue_conflict_scripts() {
        if ( ! is_admin() ) {
            return;
        }

        $conflicts = $this->detect_conflicts();
        if ( empty( $conflicts ) ) {
            return;
        }

        wp_enqueue_script( 'jquery' );
        
        // Inline script for conflict resolution
        $script = "
        function squareKitResolvePaymentConflict(action) {
            jQuery.post(ajaxurl, {
                action: 'squarekit_resolve_payment_conflict',
                conflict_action: action,
                nonce: '" . wp_create_nonce( 'squarekit_resolve_payment_conflict' ) . "'
            }, function(response) {
                if (response.success) {
                    jQuery('.squarekit-payment-conflict-notice').fadeOut();
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('Error: ' + response.data.message);
                }
            });
        }
        
        function squareKitDismissPaymentConflict() {
            jQuery('.squarekit-payment-conflict-notice').fadeOut();
        }
        ";
        
        wp_add_inline_script( 'jquery', $script );
    }

    /**
     * Get conflict resolution recommendations
     *
     * @since 1.0.0
     * @return array Array of recommendations
     */
    public function get_recommendations() {
        return array(
            'use_squarekit' => array(
                'title' => __( 'Use SquareKit (Recommended)', 'squarekit' ),
                'reasons' => array(
                    __( 'Modern Square Web Payments SDK integration', 'squarekit' ),
                    __( 'Better security with SCA compliance', 'squarekit' ),
                    __( 'Support for digital wallets (Google Pay, Apple Pay)', 'squarekit' ),
                    __( 'Active development and regular updates', 'squarekit' ),
                    __( 'Comprehensive error handling and logging', 'squarekit' )
                )
            ),
            'use_swever' => array(
                'title' => __( 'Use SWEVER', 'squarekit' ),
                'reasons' => array(
                    __( 'Legacy compatibility', 'squarekit' ),
                    __( 'Existing configuration', 'squarekit' )
                )
            )
        );
    }
}

// Initialize the payment conflict manager
new SquareKit_Payment_Conflict_Manager();
