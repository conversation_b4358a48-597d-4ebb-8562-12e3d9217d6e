<?php
/**
 * SquareKit Payment Gateway Test Interface
 * 
 * This file provides a comprehensive testing interface for validating
 * payment gateway functionality and webhook configuration.
 * 
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Load WordPress if accessed directly
    require_once dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) . '/wp-load.php';
}

// Ensure SquareKit is loaded
if ( ! class_exists( 'SquareKit' ) ) {
    require_once plugin_dir_path( __FILE__ ) . 'squarekit.php';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SquareKit Payment Gateway Tests</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f1f1f1; 
            color: #333;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 6px; 
            border-left: 4px solid;
        }
        .pass { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .fail { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .warning { 
            background: #fff3cd; 
            color: #856404; 
            border-left-color: #ffc107;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        .webhook-url { 
            background: #f8f9fa; 
            padding: 15px; 
            border: 1px solid #dee2e6; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            word-break: break-all;
            margin: 10px 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .status-card h3 {
            margin-top: 0;
            color: #495057;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        h1 { color: #23282d; margin-bottom: 30px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
        .emoji { font-size: 1.2em; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🧪</span>SquareKit Payment Gateway Tests</h1>

        <?php
        // Initialize required classes
        $settings = new SquareKit_Settings();
        $payment_conflict_manager = new SquareKit_Payment_Conflict_Manager();
        
        // Show webhook URL configuration
        echo '<h2><span class="emoji">🔗</span>Webhook Configuration</h2>';
        
        $webhook_url = get_rest_url( null, 'squarekit/v1/webhook' );
        echo '<p><strong>Webhook URL for Square Developer Dashboard:</strong></p>';
        echo "<div class='webhook-url'>{$webhook_url}</div>";
        echo '<p><em>Configure this URL in your Square Developer Dashboard under Webhooks with the following events:</em></p>';
        echo '<ul>';
        echo '<li>catalog.version.updated</li>';
        echo '<li>order.created, order.updated</li>';
        echo '<li>customer.created, customer.updated</li>';
        echo '<li>inventory.count.updated</li>';
        echo '<li>payment.created, payment.updated</li>';
        echo '<li>refund.created, refund.updated</li>';
        echo '</ul>';

        // Test gateway status
        echo '<h2><span class="emoji">📊</span>Gateway Status Overview</h2>';
        
        echo '<div class="status-grid">';
        
        // SquareKit Gateway Status
        echo '<div class="status-card">';
        echo '<h3>SquareKit Gateway</h3>';
        $squarekit_settings = get_option( 'woocommerce_squarekit_settings', array() );
        $squarekit_enabled = isset( $squarekit_settings['enabled'] ) && $squarekit_settings['enabled'] === 'yes';
        $squarekit_available = false;
        
        if ( class_exists( 'WC_Gateway_SquareKit' ) ) {
            $gateway = new WC_Gateway_SquareKit();
            $squarekit_available = $gateway->is_available();
        }
        
        echo '<p><strong>Enabled:</strong> ' . ( $squarekit_enabled ? '✅ Yes' : '❌ No' ) . '</p>';
        echo '<p><strong>Available:</strong> ' . ( $squarekit_available ? '✅ Yes' : '❌ No' ) . '</p>';
        echo '<p><strong>Title:</strong> ' . esc_html( $squarekit_settings['title'] ?? 'Square' ) . '</p>';
        echo '</div>';

        // SWEVER Gateway Status
        echo '<div class="status-card">';
        echo '<h3>SWEVER Gateway</h3>';
        $swever_settings = get_option( 'woocommerce_squaresync_credit_settings', array() );
        $swever_enabled = isset( $swever_settings['enabled'] ) && $swever_settings['enabled'] === 'yes';
        
        echo '<p><strong>Enabled:</strong> ' . ( $swever_enabled ? '✅ Yes' : '❌ No' ) . '</p>';
        echo '<p><strong>Title:</strong> ' . esc_html( $swever_settings['title'] ?? 'Square Payments by SquareSync' ) . '</p>';
        echo '</div>';

        // Square Connection Status
        echo '<div class="status-card">';
        echo '<h3>Square Connection</h3>';
        $is_connected = $settings->is_connected();
        $environment = $settings->get_environment();
        $access_token = $settings->get_access_token();
        $location_id = $settings->get_location_id();
        
        echo '<p><strong>Environment:</strong> ' . esc_html( ucfirst( $environment ) ) . '</p>';
        echo '<p><strong>Connected:</strong> ' . ( $is_connected ? '✅ Yes' : '❌ No' ) . '</p>';
        echo '<p><strong>Access Token:</strong> ' . ( $access_token ? '✅ Present' : '❌ Missing' ) . '</p>';
        echo '<p><strong>Location ID:</strong> ' . ( $location_id ? '✅ Present' : '❌ Missing' ) . '</p>';
        echo '</div>';

        // Webhook Status
        echo '<div class="status-card">';
        echo '<h3>Webhook Status</h3>';
        $webhook_status = $settings->get( 'webhook_status', false );
        $personal_token_sandbox = $settings->get( 'sandbox_personal_access_token', '' );
        $personal_token_production = $settings->get( 'production_personal_access_token', '' );
        
        echo '<p><strong>Webhooks Enabled:</strong> ' . ( $webhook_status ? '✅ Yes' : '❌ No' ) . '</p>';
        echo '<p><strong>Sandbox Personal Token:</strong> ' . ( $personal_token_sandbox ? '✅ Present' : '❌ Missing' ) . '</p>';
        echo '<p><strong>Production Personal Token:</strong> ' . ( $personal_token_production ? '✅ Present' : '❌ Missing' ) . '</p>';
        echo '</div>';
        
        echo '</div>';

        // Conflict Detection
        echo '<h2><span class="emoji">⚠️</span>Payment Gateway Conflict Detection</h2>';
        
        $conflicts = $payment_conflict_manager->detect_conflicts();
        
        if ( empty( $conflicts ) ) {
            echo '<div class="test-result pass">';
            echo '<strong>✅ No Payment Gateway Conflicts Detected</strong><br>';
            echo 'All Square payment gateways are properly configured without conflicts.';
            echo '</div>';
        } else {
            foreach ( $conflicts as $conflict ) {
                $class = $conflict['severity'] === 'high' ? 'fail' : 'warning';
                echo "<div class='test-result {$class}'>";
                echo '<strong>' . ( $conflict['severity'] === 'high' ? '❌' : '⚠️' ) . ' ' . esc_html( $conflict['message'] ) . '</strong><br>';
                
                if ( $conflict['type'] === 'multiple_gateways' && $conflict['auto_resolvable'] ) {
                    echo '<p>This conflict can be resolved automatically:</p>';
                    echo '<button class="btn btn-success" onclick="resolvePaymentConflict(\'use_squarekit\')">Use SquareKit (Recommended)</button>';
                    echo '<button class="btn btn-warning" onclick="resolvePaymentConflict(\'use_swever\')">Use SWEVER</button>';
                }
                echo '</div>';
            }
        }

        // Gateway Availability Tests
        echo '<h2><span class="emoji">🔍</span>Gateway Availability Tests</h2>';
        
        if ( class_exists( 'WC_Gateway_SquareKit' ) ) {
            $gateway = new WC_Gateway_SquareKit();
            
            // Test basic availability
            $available = $gateway->is_available();
            echo '<div class="test-result ' . ( $available ? 'pass' : 'fail' ) . '">';
            echo '<strong>' . ( $available ? '✅' : '❌' ) . ' Gateway Availability Test</strong><br>';
            echo $available ? 'SquareKit gateway is available for payments.' : 'SquareKit gateway is not available. Check configuration.';
            echo '</div>';
            
            // Test SSL requirement (production only)
            if ( $environment === 'production' ) {
                $ssl_available = is_ssl();
                echo '<div class="test-result ' . ( $ssl_available ? 'pass' : 'fail' ) . '">';
                echo '<strong>' . ( $ssl_available ? '✅' : '❌' ) . ' SSL Test (Production)</strong><br>';
                echo $ssl_available ? 'SSL is properly configured for production payments.' : 'SSL is required for production payments.';
                echo '</div>';
            }
            
            // Test currency support
            $current_currency = get_woocommerce_currency();
            $supported_currencies = array( 'USD', 'CAD', 'AUD', 'GBP', 'EUR', 'JPY' );
            $currency_supported = in_array( $current_currency, $supported_currencies );
            
            echo '<div class="test-result ' . ( $currency_supported ? 'pass' : 'fail' ) . '">';
            echo '<strong>' . ( $currency_supported ? '✅' : '❌' ) . ' Currency Support Test</strong><br>';
            echo $currency_supported ? 
                "Current currency ({$current_currency}) is supported by Square." : 
                "Current currency ({$current_currency}) is not supported by Square. Supported: " . implode( ', ', $supported_currencies );
            echo '</div>';
            
        } else {
            echo '<div class="test-result fail">';
            echo '<strong>❌ Gateway Class Not Found</strong><br>';
            echo 'WC_Gateway_SquareKit class is not available. Check if WooCommerce is active.';
            echo '</div>';
        }

        // Quick Actions
        echo '<h2><span class="emoji">🚀</span>Quick Actions</h2>';
        echo '<p>Use these buttons to quickly test and manage your payment gateway:</p>';
        
        echo '<a href="' . admin_url( 'admin.php?page=squarekit-settings&tab=connection' ) . '" class="btn">Connection Settings</a>';
        echo '<a href="' . admin_url( 'admin.php?page=squarekit-settings&tab=payment' ) . '" class="btn">Payment Settings</a>';
        echo '<a href="' . admin_url( 'admin.php?page=wc-settings&tab=checkout&section=squarekit' ) . '" class="btn">WooCommerce Gateway Settings</a>';
        echo '<button class="btn btn-success" onclick="testConnection()">Test Square Connection</button>';
        ?>

        <script>
        function resolvePaymentConflict(action) {
            if (!confirm('Are you sure you want to resolve this payment gateway conflict?')) {
                return;
            }
            
            fetch('<?php echo admin_url( 'admin-ajax.php' ); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'squarekit_resolve_payment_conflict',
                    conflict_action: action,
                    nonce: '<?php echo wp_create_nonce( 'squarekit_resolve_payment_conflict' ); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }
        
        function testConnection() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            fetch('<?php echo admin_url( 'admin-ajax.php' ); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'squarekit_test_connection',
                    nonce: '<?php echo wp_create_nonce( 'squarekit_test_connection' ); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.success ? 'Connection test successful!' : 'Connection test failed: ' + data.data.message);
            })
            .catch(error => {
                alert('Connection test failed: ' + error.message);
            })
            .finally(() => {
                btn.disabled = false;
                btn.textContent = 'Test Square Connection';
            });
        }
        </script>
    </div>
</body>
</html>
